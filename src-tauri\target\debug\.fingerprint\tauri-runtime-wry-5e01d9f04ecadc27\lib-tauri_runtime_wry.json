{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 4850902529613245248, "deps": [[376837177317575824, "softbuffer", false, 9452031639855885036], [654232091421095663, "tauri_utils", false, 10591440845926958216], [2013030631243296465, "webview2_com", false, 16687020024527186247], [3150220818285335163, "url", false, 9149914415962147110], [3722963349756955755, "once_cell", false, 395126524511392483], [4143744114649553716, "raw_window_handle", false, 4799670219169884245], [5986029879202738730, "log", false, 15765517366915710996], [8826339825490770380, "tao", false, 12424762300880502093], [9010263965687315507, "http", false, 16986275522130494756], [9141053277961803901, "wry", false, 6616657321739553994], [12304025191202589669, "build_script_build", false, 12674754355741948356], [12943761728066819757, "tauri_runtime", false, 10800498006564925613], [14585479307175734061, "windows", false, 17088300214248134231]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-5e01d9f04ecadc27\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}