{"rustc": 1842507548689473721, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 7781126298196589074, "deps": [[654232091421095663, "tauri_utils", false, 8085405789211869638], [2704937418414716471, "tauri_codegen", false, 586537837998010233], [3060637413840920116, "proc_macro2", false, 2079900143197701277], [4974441333307933176, "syn", false, 3050935563027769116], [13077543566650298139, "heck", false, 3133400880998861990], [17990358020177143287, "quote", false, 4602638336399811457]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-1835c2802ae8cc00\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}