{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2241668132362809309, "path": 1047385865093030121, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 7884894147985421462], [3150220818285335163, "url", false, 476110366268104882], [3191507132440681679, "serde_untagged", false, 14459463296869451766], [4071963112282141418, "serde_with", false, 9020983136153143959], [4899080583175475170, "semver", false, 6716009262248611394], [5986029879202738730, "log", false, 14539903849470444598], [6606131838865521726, "ctor", false, 13472640333893764312], [7170110829644101142, "json_patch", false, 11229064796071019641], [8319709847752024821, "uuid", false, 7600958528787597768], [8569119365930580996, "serde_json", false, 729615162669445349], [9010263965687315507, "http", false, 1782551203943155301], [9451456094439810778, "regex", false, 13717727011545150462], [9556762810601084293, "brotli", false, 9301681771152932876], [9689903380558560274, "serde", false, 3501869682730543704], [10806645703491011684, "thiserror", false, 2941150162954068437], [11989259058781683633, "dunce", false, 2288475746589612525], [13625485746686963219, "anyhow", false, 15777044870893721003], [15609422047640926750, "toml", false, 5128713926342026956], [15622660310229662834, "walkdir", false, 306408515801891958], [15932120279885307830, "memchr", false, 1649674830569954159], [17146114186171651583, "infer", false, 9635875284540213534], [17155886227862585100, "glob", false, 5468746062964166838], [17186037756130803222, "phf", false, 4612861699477823612]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-5858e0b497319f53\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}