["\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-57a1ef5e7ac47b23\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-57a1ef5e7ac47b23\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-57a1ef5e7ac47b23\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-57a1ef5e7ac47b23\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-57a1ef5e7ac47b23\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-57a1ef5e7ac47b23\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-57a1ef5e7ac47b23\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-57a1ef5e7ac47b23\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-57a1ef5e7ac47b23\\out\\permissions\\path\\autogenerated\\default.toml"]