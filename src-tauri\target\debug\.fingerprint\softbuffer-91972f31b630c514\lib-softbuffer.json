{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 2241668132362809309, "path": 12906264577859705267, "deps": [[376837177317575824, "build_script_build", false, 10138710337544681674], [4143744114649553716, "raw_window_handle", false, 16341307260128210312], [5986029879202738730, "log", false, 14539903849470444598], [10281541584571964250, "windows_sys", false, 12924469859060633743]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-91972f31b630c514\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}