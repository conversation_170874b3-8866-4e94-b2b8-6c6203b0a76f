{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2241668132362809309, "path": 9311417598471442982, "deps": [[654232091421095663, "tauri_utils", false, 7695265353245193155], [3150220818285335163, "url", false, 476110366268104882], [4143744114649553716, "raw_window_handle", false, 16341307260128210312], [7606335748176206944, "dpi", false, 4231345456399234902], [8569119365930580996, "serde_json", false, 729615162669445349], [9010263965687315507, "http", false, 1782551203943155301], [9689903380558560274, "serde", false, 3501869682730543704], [10806645703491011684, "thiserror", false, 2941150162954068437], [12943761728066819757, "build_script_build", false, 1115477786926781373], [14585479307175734061, "windows", false, 7082548166437213722], [16727543399706004146, "cookie", false, 16767487610196626441]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-c1e35b4a687f78e9\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}