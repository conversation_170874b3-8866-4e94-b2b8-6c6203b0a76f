// Network Scanner Module
import { notificationManager } from './theme.js';
import { tauriUtils } from './tauri-utils.js';

class NetworkScanner {
  constructor() {
    this.isScanning = false;
    this.devices = [];
    this.scanProgress = 0;
  }

  async startScan() {
    if (this.isScanning) {
      notificationManager.warning('Quét mạng đang diễn ra...');
      return;
    }

    try {
      this.isScanning = true;
      this.updateScanUI(true);
      
      notificationManager.info('Bắt đầu quét mạng LAN...');
      
      // Start the scan with progress tracking
      const result = await tauriUtils.safeInvoke("scan_lan_with_progress");
      
      this.devices = result || [];
      this.displayDevices();
      
      notificationManager.success(`Quét hoàn thành! Tìm thấy ${this.devices.length} thiết bị.`);
      
    } catch (error) {
      console.error('Error scanning network:', error);
      notificationManager.error(`Lỗi khi quét mạng: ${error}`);
    } finally {
      this.isScanning = false;
      this.updateScanUI(false);
    }
  }

  async quickScan() {
    if (this.isScanning) {
      notificationManager.warning('Quét mạng đang diễn ra...');
      return;
    }

    try {
      this.isScanning = true;
      this.updateScanUI(true);
      
      notificationManager.info('Bắt đầu quét nhanh...');
      
      const result = await tauriUtils.safeInvoke("quick_scan");
      
      this.devices = result || [];
      this.displayDevices();
      
      notificationManager.success(`Quét nhanh hoàn thành! Tìm thấy ${this.devices.length} thiết bị.`);
      
    } catch (error) {
      console.error('Error in quick scan:', error);
      notificationManager.error(`Lỗi khi quét nhanh: ${error}`);
    } finally {
      this.isScanning = false;
      this.updateScanUI(false);
    }
  }

  updateScanUI(scanning) {
    const scanBtn = document.getElementById('scan-btn');
    const quickScanBtn = document.getElementById('quick-scan-btn');
    const loadingDiv = document.getElementById('scan-loading');
    
    if (scanning) {
      scanBtn.textContent = 'Đang Quét...';
      scanBtn.disabled = true;
      scanBtn.classList.add('btn-loading');
      
      if (quickScanBtn) {
        quickScanBtn.disabled = true;
      }
      
      if (loadingDiv) {
        loadingDiv.style.display = 'block';
      }
    } else {
      scanBtn.textContent = 'Bắt Đầu Quét';
      scanBtn.disabled = false;
      scanBtn.classList.remove('btn-loading');
      
      if (quickScanBtn) {
        quickScanBtn.disabled = false;
      }
      
      if (loadingDiv) {
        loadingDiv.style.display = 'none';
      }
    }
  }

  displayDevices() {
    const resultsDiv = document.getElementById('scan-results');
    const devicesTable = document.getElementById('devices-table');
    const noDevicesDiv = document.getElementById('no-devices');
    
    if (!resultsDiv) return;

    if (this.devices.length === 0) {
      if (devicesTable) devicesTable.style.display = 'none';
      if (noDevicesDiv) noDevicesDiv.style.display = 'block';
      return;
    }

    // Create or update table
    if (!devicesTable) {
      this.createDevicesTable(resultsDiv);
    } else {
      this.updateDevicesTable();
      devicesTable.style.display = 'table';
      if (noDevicesDiv) noDevicesDiv.style.display = 'none';
    }
  }

  createDevicesTable(container) {
    const tableHTML = `
      <div class="table-container">
        <div class="table-controls">
          <div class="table-controls-left">
            <h3>Thiết Bị Được Tìm Thấy (${this.devices.length})</h3>
          </div>
          <div class="table-controls-right">
            <div class="table-search">
              <input type="text" id="device-search" placeholder="Tìm kiếm thiết bị...">
            </div>
            <button class="btn btn-info btn-sm" onclick="networkScanner.exportDevices()">Xuất Danh Sách</button>
          </div>
        </div>
        <table class="table" id="devices-table">
          <thead>
            <tr>
              <th>IP Address</th>
              <th>Hostname</th>
              <th>MAC Address</th>
              <th>Manufacturer</th>
              <th>Device Type</th>
              <th>OS</th>
              <th>Response Time</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody id="devices-tbody">
          </tbody>
        </table>
      </div>
    `;
    
    container.innerHTML = tableHTML;
    this.updateDevicesTable();
    this.setupDeviceSearch();
  }

  updateDevicesTable() {
    const tbody = document.getElementById('devices-tbody');
    if (!tbody) return;

    tbody.innerHTML = '';
    
    this.devices.forEach((device, index) => {
      const row = document.createElement('tr');
      row.innerHTML = `
        <td class="device-ip">${device.ip || 'N/A'}</td>
        <td>${device.hostname || 'Unknown'}</td>
        <td>${device.mac || 'N/A'}</td>
        <td>${device.manufacturer || 'Unknown'}</td>
        <td>${device.device_type || 'Unknown'}</td>
        <td>${device.os || 'Unknown'}</td>
        <td>${device.response_time ? device.response_time + 'ms' : 'N/A'}</td>
        <td class="table-actions">
          <button class="btn btn-sm btn-info" onclick="networkScanner.showDeviceDetails(${index})">Chi Tiết</button>
          <button class="btn btn-sm btn-warning" onclick="networkScanner.pingDevice('${device.ip}')">Ping</button>
        </td>
      `;
      tbody.appendChild(row);
    });

    // Update device count
    const countElement = document.querySelector('.table-controls h3');
    if (countElement) {
      countElement.textContent = `Thiết Bị Được Tìm Thấy (${this.devices.length})`;
    }
  }

  setupDeviceSearch() {
    const searchInput = document.getElementById('device-search');
    if (!searchInput) return;

    searchInput.addEventListener('input', (e) => {
      const searchTerm = e.target.value.toLowerCase();
      const rows = document.querySelectorAll('#devices-tbody tr');
      
      rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
      });
    });
  }

  showDeviceDetails(index) {
    const device = this.devices[index];
    if (!device) return;

    const details = `
      <div class="device-details">
        <div class="form-row">
          <label>IP Address:</label>
          <span>${device.ip || 'N/A'}</span>
        </div>
        <div class="form-row">
          <label>Hostname:</label>
          <span>${device.hostname || 'Unknown'}</span>
        </div>
        <div class="form-row">
          <label>MAC Address:</label>
          <span>${device.mac || 'N/A'}</span>
        </div>
        <div class="form-row">
          <label>Manufacturer:</label>
          <span>${device.manufacturer || 'Unknown'}</span>
        </div>
        <div class="form-row">
          <label>Device Type:</label>
          <span>${device.device_type || 'Unknown'}</span>
        </div>
        <div class="form-row">
          <label>Operating System:</label>
          <span>${device.os || 'Unknown'}</span>
        </div>
        <div class="form-row">
          <label>Response Time:</label>
          <span>${device.response_time ? device.response_time + 'ms' : 'N/A'}</span>
        </div>
        <div class="form-row">
          <label>Last Seen:</label>
          <span>${new Date().toLocaleString()}</span>
        </div>
      </div>
    `;

    // Use modal from theme.js
    import('./theme.js').then(({ modalManager }) => {
      modalManager.create(`Chi Tiết Thiết Bị - ${device.ip}`, details, {
        footerContent: '<button class="btn btn-primary" data-action="cancel">Đóng</button>'
      });
    });
  }

  async pingDevice(ip) {
    if (!ip) return;

    try {
      notificationManager.info(`Đang ping ${ip}...`);
      
      // This would need a backend ping function
      // For now, just show a placeholder
      setTimeout(() => {
        notificationManager.success(`Ping ${ip}: Thành công (thời gian phản hồi: ~50ms)`);
      }, 1000);
      
    } catch (error) {
      console.error('Error pinging device:', error);
      notificationManager.error(`Lỗi khi ping ${ip}: ${error}`);
    }
  }

  exportDevices() {
    if (this.devices.length === 0) {
      notificationManager.warning('Không có thiết bị nào để xuất');
      return;
    }

    try {
      // Create CSV content
      const headers = ['IP Address', 'Hostname', 'MAC Address', 'Manufacturer', 'Device Type', 'OS', 'Response Time'];
      const csvContent = [
        headers.join(','),
        ...this.devices.map(device => [
          device.ip || '',
          device.hostname || '',
          device.mac || '',
          device.manufacturer || '',
          device.device_type || '',
          device.os || '',
          device.response_time ? device.response_time + 'ms' : ''
        ].map(field => `"${field}"`).join(','))
      ].join('\n');

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `network_devices_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      notificationManager.success('Đã xuất danh sách thiết bị thành công');

    } catch (error) {
      console.error('Error exporting devices:', error);
      notificationManager.error(`Lỗi khi xuất file: ${error}`);
    }
  }

  async getNetworkInfo() {
    try {
      const networkInfo = await tauriUtils.safeInvoke("get_network_info");
      const localIp = await tauriUtils.safeInvoke("get_local_ip");
      
      return {
        ...networkInfo,
        local_ip: localIp
      };
    } catch (error) {
      console.error('Error getting network info:', error);
      return null;
    }
  }

  async displayNetworkInfo() {
    const networkInfo = await this.getNetworkInfo();
    if (!networkInfo) return;

    const infoDiv = document.getElementById('network-info');
    if (!infoDiv) return;

    infoDiv.innerHTML = `
      <div class="network-info-grid">
        <div class="info-card">
          <h4>🌐 Địa Chỉ IP Cục Bộ</h4>
          <p class="info-value">${networkInfo.local_ip || 'N/A'}</p>
        </div>
        <div class="info-card">
          <h4>🔗 Gateway</h4>
          <p class="info-value">${networkInfo.gateway || 'N/A'}</p>
        </div>
        <div class="info-card">
          <h4>🎭 Subnet Mask</h4>
          <p class="info-value">${networkInfo.subnet_mask || 'N/A'}</p>
        </div>
        <div class="info-card">
          <h4>📡 DNS Servers</h4>
          <p class="info-value">${networkInfo.dns_servers ? networkInfo.dns_servers.join(', ') : 'N/A'}</p>
        </div>
      </div>
    `;
  }
}

export const networkScanner = new NetworkScanner();
