{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 11493599107747556763, "profile": 17672942494452627365, "path": 4942398508502643691, "deps": [[2687125648958529997, "dns_lookup", false, 6406608310640357268], [4181933554634553783, "local_ip_address", false, 4070663608221734267], [8569119365930580996, "serde_json", false, 729615162669445349], [9689903380558560274, "serde", false, 3501869682730543704], [10697383615564341592, "rayon", false, 5839217655755766416], [12092653563678505622, "tauri", false, 7777971729793539567], [12944427623413450645, "tokio", false, 15891073255397935321], [14215103216951670585, "network_scanner_lib", false, 7449574342630985434], [14215103216951670585, "build_script_build", false, 4773571852976542926], [15844265409831675555, "mac_address", false, 11510055012578474021], [16702348383442838006, "tauri_plugin_opener", false, 11642403274872526837], [17917672826516349275, "lazy_static", false, 2898169317997073574]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\network-scanner-0f8ce4f435331147\\dep-bin-network-scanner", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}