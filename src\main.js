// Import modules
import { themeManager, notificationManager, modalManager } from './js/theme.js';
import { firewallManager } from './js/firewall.js';
import { networkScanner } from './js/scanner.js';

// Make managers globally available for HTML onclick handlers
window.firewallManager = firewallManager;
window.networkScanner = networkScanner;
window.notificationManager = notificationManager;
window.modalManager = modalManager;

async function scanLan() {
  await networkScanner.startScan();
}

// Tab switching functionality
function switchTab(tabName) {
  // Hide all sections
  document.querySelectorAll('.section').forEach(section => {
    section.classList.remove('active');
  });

  // Remove active class from all buttons
  document.querySelectorAll('.main-btn').forEach(btn => {
    btn.style.opacity = '0.7';
  });

  // Show selected section and highlight button
  document.getElementById(`${tabName}-section`).classList.add('active');
  document.getElementById(`${tabName}-tab-btn`).style.opacity = '1';

  // Load data for specific tabs
  if (tabName === 'firewall') {
    firewallManager.loadFirewallStatus();
    firewallManager.loadPortRules();
  } else if (tabName === 'app') {
    firewallManager.loadAppRules();
  }
}

// Utility functions for UI updates (called from HTML)
function updatePortInputs() {
  firewallManager.updatePortInputs();
}

function updateAppSelection() {
  firewallManager.updateAppSelection();
}

function onRunningAppSelected() {
  firewallManager.onRunningAppSelected();
}

// Event listeners setup
document.addEventListener('DOMContentLoaded', function() {
  // Tab switching
  document.getElementById('scan-tab-btn').addEventListener('click', () => switchTab('scan'));
  document.getElementById('firewall-tab-btn').addEventListener('click', () => switchTab('firewall'));
  document.getElementById('app-tab-btn').addEventListener('click', () => switchTab('app'));

  // Scan functionality
  document.getElementById('scan-btn').addEventListener('click', scanLan);

  // Port management
  const addPortBtn = document.getElementById('add-port-rule-btn');
  if (addPortBtn) {
    addPortBtn.addEventListener('click', () => firewallManager.addPortRule());
  }

  const removePortBtn = document.getElementById('remove-selected-port-rules-btn');
  if (removePortBtn) {
    removePortBtn.addEventListener('click', () => firewallManager.removeSelectedPortRules());
  }

  // App management
  const addAppBtn = document.getElementById('add-app-rule-btn');
  if (addAppBtn) {
    addAppBtn.addEventListener('click', () => firewallManager.addAppRule());
  }

  const loadAppsBtn = document.getElementById('load-running-apps-btn');
  if (loadAppsBtn) {
    loadAppsBtn.addEventListener('click', () => firewallManager.loadRunningApps());
  }

  // Radio button change handlers
  document.querySelectorAll('input[name="port-type"]').forEach(radio => {
    radio.addEventListener('change', updatePortInputs);
  });

  document.querySelectorAll('input[name="app-selection"]').forEach(radio => {
    radio.addEventListener('change', updateAppSelection);
  });

  // Initialize default tab
  switchTab('scan');

  // Load initial data after a short delay to ensure Tauri API is ready
  setTimeout(() => {
    networkScanner.displayNetworkInfo();
  }, 200);
});

// Make functions globally available for HTML onclick handlers
window.switchTab = switchTab;
window.scanLan = scanLan;
window.updatePortInputs = updatePortInputs;
window.updateAppSelection = updateAppSelection;
window.onRunningAppSelected = onRunningAppSelected;
