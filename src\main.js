const { invoke } = window.__TAURI__.core;
const { listen } = window.__TAURI__.event;

let unlistenProgress;
let unlistenDeviceFound;

async function setupListeners() {
  unlistenProgress = await listen('scan_progress', (event) => {
    const progress = event.payload;
    const progressText = document.getElementById("scan-progress-text");
    if (progressText) {
      progressText.textContent = `Đang quét: ${progress.scanning_ip} (${progress.current}/${progress.total}) | Tìm thấy: ${progress.found_devices}`;
    }
  });

  unlistenDeviceFound = await listen('device_found', (event) => {
    const dev = event.payload;
    const table = document.getElementById("devices-table");
    const tbody = table.querySelector("tbody");
    
    // Make sure table is visible if this is the first device
    if (table.style.display === "none") {
        table.style.display = "table";
    }

    // Remove "no devices found" message if it exists
    const noDeviceRow = tbody.querySelector(".no-devices");
    if (noDeviceRow) {
        noDeviceRow.remove();
    }

    const tr = document.createElement("tr");
    tr.innerHTML = `<td>${dev.ip}</td><td>${dev.hostname || ""}</td><td>${dev.mac || ""}</td><td>${dev.os || ""}</td>`;
    tbody.appendChild(tr);
  });
}

function cleanupListeners() {
    if (unlistenProgress) {
        unlistenProgress();
        unlistenProgress = null;
    }
    if (unlistenDeviceFound) {
        unlistenDeviceFound();
        unlistenDeviceFound = null;
    }
}

async function scanLan() {
  const scanBtn = document.getElementById("scan-btn");
  const table = document.getElementById("devices-table");
  const tbody = table.querySelector("tbody");
  const loading = document.getElementById("scan-loading");
  const progressText = document.getElementById("scan-progress-text");

  // --- Reset UI ---
  scanBtn.disabled = true;
  tbody.innerHTML = ""; // Clear previous results
  progressText.textContent = "";
  table.style.display = "none";
  loading.style.display = "block";
  
  // --- Setup Listeners ---
  await setupListeners();

  // --- Start Scan ---
  invoke("scan_lan_with_progress", { config: null })
    .then((finalDevices) => {
      console.log("Scan finished.", finalDevices);
      if (tbody.children.length === 0) {
        tbody.innerHTML = '<tr><td colspan="4" class="no-devices">Không tìm thấy thiết bị nào.</td></tr>';
        table.style.display = "table";
      }
    })
    .catch((e) => {
      console.error("Scan error:", e);
      tbody.innerHTML = `<tr><td colspan="4">Lỗi: ${e}</td></tr>`;
      table.style.display = "table";
    })
    .finally(() => {
      loading.style.display = "none";
      scanBtn.disabled = false;
      cleanupListeners(); // Clean up listeners when scan is done
    });
}

window.addEventListener("DOMContentLoaded", () => {
  document.getElementById("scan-btn").addEventListener("click", scanLan);
});
