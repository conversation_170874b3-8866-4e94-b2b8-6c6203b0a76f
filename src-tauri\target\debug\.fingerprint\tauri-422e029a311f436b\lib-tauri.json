{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 2241668132362809309, "path": 18393454837022506084, "deps": [[40386456601120721, "percent_encoding", false, 3237466966758669628], [654232091421095663, "tauri_utils", false, 7695265353245193155], [1200537532907108615, "url<PERSON><PERSON>n", false, 7884894147985421462], [2013030631243296465, "webview2_com", false, 9214410664057349386], [3150220818285335163, "url", false, 476110366268104882], [3331586631144870129, "getrandom", false, 10461987654385219991], [4143744114649553716, "raw_window_handle", false, 16341307260128210312], [4494683389616423722, "muda", false, 15789963333177223135], [4919829919303820331, "serialize_to_javascript", false, 10037529101642433577], [5986029879202738730, "log", false, 14539903849470444598], [8569119365930580996, "serde_json", false, 729615162669445349], [9010263965687315507, "http", false, 1782551203943155301], [9689903380558560274, "serde", false, 3501869682730543704], [10229185211513642314, "mime", false, 28263485832331148], [10806645703491011684, "thiserror", false, 2941150162954068437], [11989259058781683633, "dunce", false, 2288475746589612525], [12092653563678505622, "build_script_build", false, 3255094644836121540], [12304025191202589669, "tauri_runtime_wry", false, 12238167495858901377], [12565293087094287914, "window_vibrancy", false, 7039624141302178164], [12943761728066819757, "tauri_runtime", false, 17857731071459737596], [12944427623413450645, "tokio", false, 15891073255397935321], [12986574360607194341, "serde_repr", false, 15342612029808804463], [13077543566650298139, "heck", false, 8708717942802222969], [13405681745520956630, "tauri_macros", false, 17101214944476967894], [13625485746686963219, "anyhow", false, 15777044870893721003], [14585479307175734061, "windows", false, 7082548166437213722], [16928111194414003569, "dirs", false, 7682316150657083483], [17155886227862585100, "glob", false, 5468746062964166838]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-422e029a311f436b\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}