<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>LAN Scanner & Firewall Manager</title>
    <script type="module" src="/main.js" defer></script>
    <link rel="stylesheet" href="styles.css" />
    <style>
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .button-group { display: flex; gap: 20px; margin-bottom: 30px; }
        .main-btn { 
            padding: 15px 30px; 
            font-size: 16px; 
            border: none; 
            border-radius: 8px; 
            cursor: pointer;
            transition: all 0.3s;
        }
        .scan-btn { background: #4CAF50; color: white; }
        .firewall-btn { background: #2196F3; color: white; }
        .app-btn { background: #FF9800; color: white; }
        .main-btn:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
        .section { display: none; margin-top: 20px; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .section.active { display: block; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group select { 
            width: 100%; 
            padding: 8px; 
            border: 1px solid #ddd; 
            border-radius: 4px; 
        }
        .action-btn { 
            padding: 8px 16px; 
            margin: 5px; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
        }
        .add-btn { background: #4CAF50; color: white; }
        .remove-btn { background: #f44336; color: white; }
        .list-btn { background: #2196F3; color: white; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
        th { background-color: #f2f2f2; }
        .result-area { 
            margin-top: 20px; 
            padding: 15px; 
            background: #f9f9f9; 
            border-radius: 4px; 
            min-height: 100px; 
        }
    </style>
</head>
<body>
    <main class="container">
        <h1>LAN Scanner & Firewall Manager</h1>
        
        <div class="button-group">
            <button id="scan-tab-btn" class="main-btn scan-btn">Quét Mạng LAN</button>
            <button id="firewall-tab-btn" class="main-btn firewall-btn">Quản Lý Port</button>
            <button id="app-tab-btn" class="main-btn app-btn">Quản Lý Ứng Dụng</button>
        </div>

        <!-- LAN Scanner Section -->
        <div id="scan-section" class="section active">
            <h2>Quét Thiết Bị Mạng LAN</h2>
            <button id="scan-btn" class="action-btn add-btn">Bắt Đầu Quét</button>
            
            <div id="scan-loading" style="display:none">
                <p>Đang quét mạng LAN...</p>
                <p id="scan-progress-text"></p>
            </div>
            
            <table id="devices-table" style="display:none">
                <thead>
                    <tr>
                        <th>IP</th>
                        <th>Hostname</th>
                        <th>MAC</th>
                        <th>OS</th>
                        <th>Device Type</th>
                        <th>Manufacturer</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>

        <!-- Firewall Port Management Section -->
        <div id="firewall-section" class="section">
            <h2>Quản Lý Port Tường Lửa</h2>
            
            <div class="form-group">
                <label>Tên Rule:</label>
                <input type="text" id="port-rule-name" placeholder="Nhập tên rule">
            </div>
            
            <div class="form-group">
                <label>Port:</label>
                <input type="number" id="port-number" placeholder="Nhập số port (VD: 80)">
            </div>
            
            <div class="form-group">
                <label>Protocol:</label>
                <select id="port-protocol">
                    <option value="TCP">TCP</option>
                    <option value="UDP">UDP</option>
                </select>
            </div>
            
            <div class="form-group">
                <label>Action:</label>
                <select id="port-action">
                    <option value="ALLOW">Cho phép</option>
                    <option value="BLOCK">Chặn</option>
                </select>
            </div>
            
            <div class="form-group">
                <label>Direction:</label>
                <select id="port-direction">
                    <option value="IN">Inbound</option>
                    <option value="OUT">Outbound</option>
                </select>
            </div>
            
            <button id="add-port-rule" class="action-btn add-btn">Thêm Rule</button>
            <button id="remove-port-rule" class="action-btn remove-btn">Xóa Rule</button>
            <button id="list-port-rules" class="action-btn list-btn">Xem Danh Sách</button>
            
            <div id="port-result" class="result-area"></div>
        </div>

        <!-- Firewall App Management Section -->
        <div id="app-section" class="section">
            <h2>Quản Lý Ứng Dụng Tường Lửa</h2>
            
            <div class="form-group">
                <label>Tên Ứng Dụng:</label>
                <input type="text" id="app-name" placeholder="Nhập tên ứng dụng">
            </div>
            
            <div class="form-group">
                <label>Đường Dẫn:</label>
                <input type="text" id="app-path" placeholder="VD: C:\Program Files\App\app.exe">
            </div>
            
            <div class="form-group">
                <label>Action:</label>
                <select id="app-action">
                    <option value="ALLOW">Cho phép</option>
                    <option value="BLOCK">Chặn</option>
                </select>
            </div>
            
            <button id="add-app-rule" class="action-btn add-btn">Thêm Ứng Dụng</button>
            <button id="remove-app-rule" class="action-btn remove-btn">Xóa Ứng Dụng</button>
            
            <div id="app-result" class="result-area"></div>
        </div>
    </main>

    <script type="module">
        import $ from "https://unpkg.com/fwkuijs@1.0.19/fw/index.js";
        $.start({css:"", exNames:[], gValue: {},  valueExt: {}});
    </script>
</body>
</html>

