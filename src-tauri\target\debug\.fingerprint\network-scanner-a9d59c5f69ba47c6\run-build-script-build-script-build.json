{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[14215103216951670585, "build_script_build", false, 8376607254245247264], [12092653563678505622, "build_script_build", false, 3255094644836121540], [16702348383442838006, "build_script_build", false, 4267837915581272790]], "local": [{"RerunIfChanged": {"output": "debug\\build\\network-scanner-a9d59c5f69ba47c6\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}