{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 11493599107747556763, "profile": 8731458305071235362, "path": 4942398508502643691, "deps": [[2687125648958529997, "dns_lookup", false, 15947894574290644768], [4181933554634553783, "local_ip_address", false, 11104107957952025115], [8569119365930580996, "serde_json", false, 2806873895409105749], [9689903380558560274, "serde", false, 8511114661884439858], [10697383615564341592, "rayon", false, 397538773101446094], [12092653563678505622, "tauri", false, 8844139675115163231], [12944427623413450645, "tokio", false, 13902341140467724027], [14215103216951670585, "network_scanner_lib", false, 11883183207768913767], [14215103216951670585, "build_script_build", false, 9283367951243246813], [15844265409831675555, "mac_address", false, 4377469034934081621], [16702348383442838006, "tauri_plugin_opener", false, 2871676536211367547], [17917672826516349275, "lazy_static", false, 1363981883198100928]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\network-scanner-6c08299313b87f98\\dep-bin-network-scanner", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}