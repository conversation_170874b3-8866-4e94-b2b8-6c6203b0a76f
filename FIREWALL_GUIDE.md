# Hướng Dẫn Sử Dụng Tính Năng Quản Lý Firewall

## Tổng Quan

Ứng dụng Network Scanner hiện đã được tích hợp đầy đủ các tính năng quản lý firewall tương tự như Windows Firewall, bao gồm:

- **Quản lý Port**: Cho phép mở/chặn các port cụ thể hoặc dải port
- **Quản lý Ứng dụng**: <PERSON><PERSON><PERSON> soát quyền truy cập mạng của các ứng dụng
- **Hiển thị trạng thái**: <PERSON> dõi trạng thái firewall theo từng profile

## 🛡️ Quản Lý Port Tường Lửa

### Các Tính Năng Chính:

#### 1. **Cấu hình Port linh hoạt**
- **Port đơn**: Nhập một port cụ thể (VD: 80, 443)
- **Dải port**: Nhập dải port (VD: 8000-8080)
- **Tất cả port**: Áp dụng rule cho mọi port

#### 2. **Protocol hỗ trợ**
- TCP
- UDP  
- TCP & UDP (cả hai)

#### 3. **Hành động**
- **Cho phép (Allow)**: Mở port/dải port
- **Chặn (Block)**: Chặn hoàn toàn
- **Mở nếu an toàn**: Chỉ cho phép kết nối từ mạng nội bộ

#### 4. **Hướng kết nối**
- **Inbound (Vào)**: Kết nối từ bên ngoài vào
- **Outbound (Ra)**: Kết nối từ trong ra ngoài
- **Cả hai hướng**: Áp dụng cho cả in và out

#### 5. **Phạm vi áp dụng**
- **Tất cả địa chỉ**: Không giới hạn IP
- **Chỉ mạng nội bộ**: Chỉ áp dụng cho LAN
- **Tùy chỉnh IP**: Nhập subnet cụ thể (VD: ***********/24)

### Cách Sử Dụng:

1. **Thêm Rule Port:**
   - Nhập tên rule (VD: "Web Server")
   - Chọn loại port (đơn/dải/tất cả)
   - Nhập port hoặc dải port
   - Chọn protocol (TCP/UDP)
   - Chọn hành động (Allow/Block/Allow Secure)
   - Chọn hướng và phạm vi
   - Click "Thêm Rule"

2. **Quản lý Rules:**
   - Xem danh sách rules hiện có
   - Chọn multiple rules để xóa hàng loạt
   - Sử dụng checkbox "Chọn tất cả"
   - Click "Làm Mới Danh Sách" để cập nhật

## 📱 Quản Lý Ứng Dụng Tường Lửa

### Các Tính Năng Chính:

#### 1. **Cách chọn ứng dụng**
- **Nhập đường dẫn**: Gõ trực tiếp path đến file .exe
- **Duyệt file**: Sử dụng file browser (sẽ phát triển)
- **Chọn từ ứng dụng đang chạy**: Chọn từ danh sách processes

#### 2. **Hành động**
- **Cho phép (Allow)**: Ứng dụng có thể truy cập mạng
- **Chặn (Block)**: Chặn hoàn toàn truy cập mạng
- **Mở nếu an toàn**: Chỉ cho phép kết nối nội bộ

#### 3. **Phạm vi áp dụng**
- **Tất cả kết nối**: Không giới hạn
- **Chỉ mạng nội bộ**: Chỉ cho phép LAN
- **Chỉ Internet**: Chỉ cho phép kết nối ra ngoài

### Cách Sử Dụng:

1. **Thêm Rule Ứng Dụng:**
   - Nhập tên ứng dụng
   - Chọn cách chọn app (manual/browse/running)
   - Nhập hoặc chọn đường dẫn ứng dụng
   - Chọn hành động và phạm vi
   - Thêm ghi chú (tùy chọn)
   - Click "Thêm Rule Ứng Dụng"

2. **Làm việc với Running Apps:**
   - Click radio "Chọn từ ứng dụng đang chạy"
   - Click "Làm Mới" để tải danh sách
   - Chọn ứng dụng từ dropdown

## 📊 Trạng Thái Tường Lửa

### Hiển thị thông tin:
- **Domain Profile**: Trạng thái firewall cho domain
- **Private Profile**: Trạng thái cho mạng riêng
- **Public Profile**: Trạng thái cho mạng công cộng
- **Trạng thái tổng thể**: Tổng hợp tất cả profiles

### Các trạng thái:
- 🛡️ **Enabled** (Màu xanh): Firewall đang hoạt động
- ❌ **Disabled** (Màu đỏ): Firewall bị tắt
- ❓ **Unknown** (Màu cam): Không xác định được

## ⚠️ Lưu Ý Quan Trọng

### Quyền Administrator:
- **Windows**: Cần chạy ứng dụng với quyền Administrator để quản lý firewall
- Nếu không có quyền, các thao tác sẽ báo lỗi

### Tương thích:
- **Windows**: Đầy đủ tính năng (sử dụng netsh)
- **Linux/macOS**: Một số tính năng bị giới hạn

### An toàn:
- Tất cả rules được tạo với prefix "LAN_Scanner_" để dễ nhận biết
- Có thể xóa rules bằng Windows Firewall nếu cần
- Luôn backup cấu hình firewall trước khi thay đổi lớn

## 🔧 Khắc Phục Sự Cố

### Lỗi thường gặp:

1. **"Access is denied"**
   - Chạy ứng dụng với quyền Administrator
   - Click chuột phải → "Run as administrator"

2. **"Không thể lấy danh sách rules"**
   - Kiểm tra Windows Firewall service có đang chạy
   - Restart Windows Firewall service

3. **Rules không hiển thị**
   - Click "Làm Mới Danh Sách"
   - Kiểm tra tên rule có prefix "LAN_Scanner_"

### Tips sử dụng:

- Đặt tên rule có ý nghĩa để dễ quản lý
- Sử dụng "Mở nếu an toàn" cho các ứng dụng không rõ nguồn gốc
- Thường xuyên kiểm tra trạng thái firewall
- Backup danh sách rules quan trọng

## 🚀 Tính Năng Sắp Tới

- File browser để chọn ứng dụng
- Import/Export cấu hình firewall
- Lịch sử thay đổi rules
- Thống kê traffic theo rules
- Hỗ trợ Linux iptables
- Notification khi có kết nối bị chặn

---

**Phiên bản**: 1.0  
**Cập nhật**: 29/07/2025  
**Hỗ trợ**: Windows 10/11, Windows Server 2016+
