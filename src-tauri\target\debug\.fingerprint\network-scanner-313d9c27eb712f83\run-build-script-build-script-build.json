{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[14215103216951670585, "build_script_build", false, 3404438261492351464], [12092653563678505622, "build_script_build", false, 12216727744425479957], [16702348383442838006, "build_script_build", false, 11976377812117319033]], "local": [{"RerunIfChanged": {"output": "debug\\build\\network-scanner-313d9c27eb712f83\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": "{\"build\":{\"devUrl\":\"http://127.0.0.1:1430\"}}"}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}