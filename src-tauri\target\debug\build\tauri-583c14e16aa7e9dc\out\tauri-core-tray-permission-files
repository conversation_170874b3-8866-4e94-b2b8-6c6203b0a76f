["\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\tray\\autogenerated\\commands\\get_by_id.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\tray\\autogenerated\\commands\\new.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\tray\\autogenerated\\commands\\remove_by_id.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\tray\\autogenerated\\commands\\set_icon.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\tray\\autogenerated\\commands\\set_icon_as_template.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\tray\\autogenerated\\commands\\set_menu.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\tray\\autogenerated\\commands\\set_show_menu_on_left_click.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\tray\\autogenerated\\commands\\set_temp_dir_path.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\tray\\autogenerated\\commands\\set_title.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\tray\\autogenerated\\commands\\set_tooltip.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\tray\\autogenerated\\commands\\set_visible.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\tray\\autogenerated\\default.toml"]