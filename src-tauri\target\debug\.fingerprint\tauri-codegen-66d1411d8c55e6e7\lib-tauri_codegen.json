{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 44390266975535834, "deps": [[654232091421095663, "tauri_utils", false, 8085405789211869638], [3060637413840920116, "proc_macro2", false, 2079900143197701277], [3150220818285335163, "url", false, 459152159102548778], [4899080583175475170, "semver", false, 4831674333348931428], [4974441333307933176, "syn", false, 3050935563027769116], [7170110829644101142, "json_patch", false, 5047771166925430042], [7392050791754369441, "ico", false, 18389635304842405129], [8319709847752024821, "uuid", false, 5810210022531892840], [8569119365930580996, "serde_json", false, 16182023219926506035], [9556762810601084293, "brotli", false, 7772046722291759537], [9689903380558560274, "serde", false, 9246617389178666882], [9857275760291862238, "sha2", false, 2227535853191461048], [10806645703491011684, "thiserror", false, 10064581045881432056], [12687914511023397207, "png", false, 3224964722773537587], [13077212702700853852, "base64", false, 17182321280524970086], [15622660310229662834, "walkdir", false, 1349346429262320482], [17990358020177143287, "quote", false, 4602638336399811457]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-66d1411d8c55e6e7\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}