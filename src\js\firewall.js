// Firewall Management Module
import { notificationManager, modalManager } from './theme.js';

class FirewallManager {
  constructor() {
    this.portRules = [];
    this.appRules = [];
    this.firewallStatus = null;
  }

  // Port Management
  async addPortRule() {
    const { invoke } = window.__TAURI__.tauri;
    try {
      const ruleName = document.getElementById('port-rule-name').value.trim();
      const portType = document.querySelector('input[name="port-type"]:checked').value;
      const portValue = document.getElementById('port-value').value.trim();
      const protocol = document.getElementById('port-protocol').value;
      const action = document.getElementById('port-action').value;
      const direction = document.getElementById('port-direction').value;
      const scope = document.getElementById('port-scope').value;

      // Validation
      if (!ruleName) {
        notificationManager.error('Vui lòng nhập tên rule');
        return;
      }

      if (portType !== 'all' && !portValue) {
        notificationManager.error('Vui lòng nhập port hoặc dải port');
        return;
      }

      // Validate port format
      if (portType === 'single' && portValue) {
        const port = parseInt(portValue);
        if (isNaN(port) || port < 1 || port > 65535) {
          notificationManager.error('Port phải là số từ 1 đến 65535');
          return;
        }
      }

      if (portType === 'range' && portValue) {
        const rangeParts = portValue.split('-');
        if (rangeParts.length !== 2) {
          notificationManager.error('Dải port phải có định dạng: start-end (VD: 8000-8080)');
          return;
        }
        const startPort = parseInt(rangeParts[0]);
        const endPort = parseInt(rangeParts[1]);
        if (isNaN(startPort) || isNaN(endPort) || startPort >= endPort || startPort < 1 || endPort > 65535) {
          notificationManager.error('Dải port không hợp lệ');
          return;
        }
      }

      const result = await invoke("manage_firewall_port", {
        ruleName,
        port: portType === 'all' ? 'all' : portValue,
        protocol,
        action,
        direction,
        scope
      });

      notificationManager.success(`Đã thêm rule port: ${ruleName}`);
      this.clearPortForm();
      await this.loadPortRules();

    } catch (error) {
      console.error('Error adding port rule:', error);
      notificationManager.error(`Lỗi khi thêm rule: ${error}`);
    }
  }

  async loadPortRules() {
    const { invoke } = window.__TAURI__.tauri;
    try {
      const rules = await invoke("get_firewall_rules");
      const portRulesTable = document.getElementById('port-rules-table');
      const noPortRulesDiv = document.getElementById('no-port-rules');
      const tbody = portRulesTable.querySelector('tbody');

      if (rules.length === 0) {
        portRulesTable.style.display = 'none';
        noPortRulesDiv.style.display = 'block';
        return;
      }

      tbody.innerHTML = '';
      rules.forEach((rule, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td><input type="checkbox" data-rule="${rule}"></td>
          <td>${rule}</td>
          <td>Auto</td>
          <td>Auto</td>
          <td>Auto</td>
          <td><span class="status-badge status-enabled">Enabled</span></td>
          <td class="table-actions">
            <button class="btn btn-sm btn-warning" onclick="firewallManager.editPortRule('${rule}')">Sửa</button>
          </td>
        `;
        tbody.appendChild(row);
      });

      portRulesTable.style.display = 'table';
      noPortRulesDiv.style.display = 'none';

    } catch (error) {
      console.error('Error loading port rules:', error);
      notificationManager.error(`Lỗi khi tải danh sách rules: ${error}`);
    }
  }

  async removeSelectedPortRules() {
    const { invoke } = window.__TAURI__.tauri;
    const checkboxes = document.querySelectorAll('#port-rules-table input[type="checkbox"]:checked');
    if (checkboxes.length === 0) {
      notificationManager.warning('Vui lòng chọn ít nhất một rule để xóa');
      return;
    }

    const ruleNames = Array.from(checkboxes).map(cb => cb.dataset.rule);
    
    modalManager.confirm(
      'Xác nhận xóa',
      `Bạn có chắc muốn xóa ${ruleNames.length} rule(s) đã chọn?`,
      async () => {
        try {
          for (const ruleName of ruleNames) {
            await invoke("manage_firewall_port", {
              ruleName,
              port: "",
              protocol: "TCP",
              action: "remove",
              direction: "in",
              scope: "all"
            });
          }
          notificationManager.success(`Đã xóa ${ruleNames.length} rule(s)`);
          await this.loadPortRules();
        } catch (error) {
          console.error('Error removing rules:', error);
          notificationManager.error(`Lỗi khi xóa rules: ${error}`);
        }
      }
    );
  }

  editPortRule(ruleName) {
    notificationManager.info(`Chức năng chỉnh sửa rule "${ruleName}" sẽ được phát triển trong phiên bản tiếp theo.`);
  }

  clearPortForm() {
    document.getElementById('port-rule-name').value = '';
    document.getElementById('port-value').value = '';
    document.querySelector('input[name="port-type"][value="single"]').checked = true;
    document.getElementById('port-protocol').value = 'TCP';
    document.getElementById('port-action').value = 'allow';
    document.getElementById('port-direction').value = 'in';
    document.getElementById('port-scope').value = 'all';
    this.updatePortInputs();
  }

  updatePortInputs() {
    const portType = document.querySelector('input[name="port-type"]:checked').value;
    const portInput = document.getElementById('port-value');
    const portLabel = document.querySelector('label[for="port-value"]');
    
    if (portType === 'all') {
      portInput.disabled = true;
      portInput.placeholder = 'Tất cả port';
      portLabel.textContent = 'Port: (Tự động - Tất cả port)';
    } else if (portType === 'single') {
      portInput.disabled = false;
      portInput.placeholder = 'VD: 80, 443, 8080';
      portLabel.textContent = 'Port:';
    } else if (portType === 'range') {
      portInput.disabled = false;
      portInput.placeholder = 'VD: 8000-8080';
      portLabel.textContent = 'Dải Port:';
    }
  }

  // Application Management
  async addAppRule() {
    const { invoke } = window.__TAURI__.tauri;
    try {
      const appName = document.getElementById('app-name').value.trim();
      const appSelectionType = document.querySelector('input[name="app-selection"]:checked').value;
      const appPath = document.getElementById('app-path').value.trim();
      const appAction = document.getElementById('app-action').value;
      const appScope = document.getElementById('app-scope').value;
      const appNotes = document.getElementById('app-notes').value.trim();

      // Validation
      if (!appName) {
        notificationManager.error('Vui lòng nhập tên ứng dụng');
        return;
      }

      if (appSelectionType !== 'all' && !appPath) {
        notificationManager.error('Vui lòng nhập đường dẫn ứng dụng');
        return;
      }

      const result = await invoke("manage_firewall_app", {
        ruleName: appName,
        appPath: appSelectionType === 'all' ? 'all' : appPath,
        action: appAction,
        scope: appScope,
        notes: appNotes
      });

      notificationManager.success(`Đã thêm rule ứng dụng: ${appName}`);
      this.clearAppForm();
      await this.loadAppRules();

    } catch (error) {
      console.error('Error adding app rule:', error);
      notificationManager.error(`Lỗi khi thêm rule: ${error}`);
    }
  }

  async loadAppRules() {
    // This would need a backend function to list app rules
    // For now, just show a placeholder
    const table = document.getElementById('app-rules-table');
    const noRulesDiv = document.getElementById('no-app-rules');
    
    table.style.display = 'none';
    noRulesDiv.style.display = 'block';
  }

  async loadRunningApps() {
    const { invoke } = window.__TAURI__.tauri;
    try {
      const processes = await invoke("get_running_processes");
      const select = document.getElementById('running-apps-select');
      
      select.innerHTML = '<option value="">Chọn ứng dụng...</option>';
      
      processes.forEach(process => {
        const option = document.createElement('option');
        option.value = process.path;
        option.textContent = `${process.name} (${process.path})`;
        select.appendChild(option);
      });

      notificationManager.success(`Đã tải ${processes.length} ứng dụng đang chạy`);

    } catch (error) {
      console.error('Error loading running apps:', error);
      notificationManager.error(`Lỗi khi tải danh sách ứng dụng: ${error}`);
    }
  }

  updateAppSelection() {
    const appSelectionType = document.querySelector('input[name="app-selection"]:checked').value;
    const pathInput = document.getElementById('app-path');
    const runningAppsDiv = document.getElementById('running-apps-selection');
    
    if (appSelectionType === 'manual') {
      pathInput.style.display = 'block';
      runningAppsDiv.style.display = 'none';
      pathInput.disabled = false;
      pathInput.placeholder = 'VD: C:\\Program Files\\Google\\Chrome\\chrome.exe';
    } else if (appSelectionType === 'browse') {
      pathInput.style.display = 'block';
      runningAppsDiv.style.display = 'none';
      pathInput.disabled = true;
      pathInput.placeholder = 'Sử dụng nút Browse để chọn file...';
      notificationManager.info('Chức năng Browse file sẽ được phát triển trong phiên bản tiếp theo');
    } else if (appSelectionType === 'running') {
      pathInput.style.display = 'none';
      runningAppsDiv.style.display = 'block';
    }
  }

  onRunningAppSelected() {
    const select = document.getElementById('running-apps-select');
    const pathInput = document.getElementById('app-path');
    pathInput.value = select.value;
  }

  clearAppForm() {
    document.getElementById('app-name').value = '';
    document.getElementById('app-path').value = '';
    document.getElementById('app-notes').value = '';
    document.querySelector('input[name="app-selection"][value="manual"]').checked = true;
    document.getElementById('app-action').value = 'allow';
    document.getElementById('app-scope').value = 'all';
    this.updateAppSelection();
  }

  // Firewall Status
  async loadFirewallStatus() {
    const { invoke } = window.__TAURI__.tauri;
    try {
      const status = await invoke("get_firewall_status");
      const statusDisplay = document.getElementById('firewall-status-display');
      
      const getStatusColor = (state) => {
        if (state === 'Enabled') return 'var(--status-enabled)';
        if (state === 'Disabled') return 'var(--status-disabled)';
        return 'var(--status-unknown)';
      };
      
      const getStatusIcon = (state) => {
        if (state === 'Enabled') return '🛡️';
        if (state === 'Disabled') return '❌';
        return '❓';
      };
      
      statusDisplay.innerHTML = `
        <div class="status-grid">
          <div class="status-card">
            <div class="status-icon">${getStatusIcon(status.domain)}</div>
            <div class="status-text" style="color: ${getStatusColor(status.domain)};">${status.domain}</div>
            <div class="status-label">Domain Profile</div>
          </div>
          <div class="status-card">
            <div class="status-icon">${getStatusIcon(status.private)}</div>
            <div class="status-text" style="color: ${getStatusColor(status.private)};">${status.private}</div>
            <div class="status-label">Private Profile</div>
          </div>
          <div class="status-card">
            <div class="status-icon">${getStatusIcon(status.public)}</div>
            <div class="status-text" style="color: ${getStatusColor(status.public)};">${status.public}</div>
            <div class="status-label">Public Profile</div>
          </div>
        </div>
        <div class="status-overall" style="border-color: ${getStatusColor(status.overall)};">
          <strong>Trạng thái tổng thể: <span style="color: ${getStatusColor(status.overall)};">${status.overall}</span></strong>
        </div>
      `;
    } catch (error) {
      console.error('Error loading firewall status:', error);
      document.getElementById('firewall-status-display').innerHTML = `
        <p style="color: var(--color-danger);">Lỗi khi tải trạng thái firewall: ${error}</p>
      `;
    }
  }

  // Utility functions
  toggleSelectAllPortRules() {
    const selectAllCheckbox = document.getElementById('select-all-port-rules');
    const ruleCheckboxes = document.querySelectorAll('#port-rules-table tbody input[type="checkbox"]');
    
    ruleCheckboxes.forEach(checkbox => {
      checkbox.checked = selectAllCheckbox.checked;
    });
  }

  toggleSelectAllAppRules() {
    const selectAllCheckbox = document.getElementById('select-all-app-rules');
    const ruleCheckboxes = document.querySelectorAll('#app-rules-table tbody input[type="checkbox"]');
    
    ruleCheckboxes.forEach(checkbox => {
      checkbox.checked = selectAllCheckbox.checked;
    });
  }
}

export const firewallManager = new FirewallManager();
