cargo:rerun-if-changed=build.rs
cargo:rerun-if-env-changed=WINAPI_NO_BUNDLED_LIBRARIES
cargo:rerun-if-env-changed=WINAPI_STATIC_NOBUNDLE
cargo:rustc-cfg=feature="ipexport"
cargo:rustc-cfg=feature="ifmib"
cargo:rustc-cfg=feature="ifdef"
cargo:rustc-cfg=feature="winnt"
cargo:rustc-cfg=feature="iprtrmib"
cargo:rustc-cfg=feature="minwinbase"
cargo:rustc-cfg=feature="in6addr"
cargo:rustc-cfg=feature="udpmib"
cargo:rustc-cfg=feature="inaddr"
cargo:rustc-cfg=feature="iptypes"
cargo:rustc-cfg=feature="tcpmib"
cargo:rustc-cfg=feature="excpt"
cargo:rustc-cfg=feature="ntstatus"
cargo:rustc-cfg=feature="ntdef"
cargo:rustc-cfg=feature="tcpestats"
cargo:rustc-cfg=feature="corecrt"
cargo:rustc-cfg=feature="ktmtypes"
cargo:rustc-cfg=feature="wtypesbase"
cargo:rustc-cfg=feature="nldef"
cargo:rustc-cfg=feature="guiddef"
cargo:rustc-cfg=feature="rpcndr"
cargo:rustc-cfg=feature="minwindef"
cargo:rustc-cfg=feature="ipmib"
cargo:rustc-cfg=feature="vcruntime"
cargo:rustc-cfg=feature="ipifcons"
cargo:rustc-cfg=feature="basetsd"
cargo:rustc-cfg=feature="ws2ipdef"
cargo:rustc-link-lib=dylib=iphlpapi
cargo:rustc-link-lib=dylib=kernel32
