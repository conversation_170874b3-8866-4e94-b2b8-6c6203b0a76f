// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use tauri::{command, Emitter, Window};
use std::net::{IpAddr, SocketAddr, TcpStream};
use std::process::Command;
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use std::sync::{Arc, Mutex};
use local_ip_address::local_ip;
use mac_address;
use rayon::prelude::*;
use serde::{Serialize, Deserialize};
use std::collections::HashMap;

#[derive(Serialize, Deserialize, Clone, Debug)]
struct DeviceInfo {
    ip: String,
    hostname: Option<String>,
    mac: Option<String>,
    os: Option<String>,
    manufacturer: Option<String>,
    ports: Option<Vec<u16>>,
    response_time: Option<u64>, // milliseconds
    last_seen: u64, // timestamp
    device_type: Option<String>,
}

#[derive(Serialize, Deserialize, Clone)]
struct ScanProgress {
    current: usize,
    total: usize,
    found_devices: usize,
    scanning_ip: String,
}

#[derive(Serialize, Deserialize)]
struct ScanConfig {
    timeout_ms: u64,
    max_concurrent: usize,
    scan_ports: bool,
    common_ports: Vec<u16>,
    fast_mode: bool,
}

impl Default for ScanConfig {
    fn default() -> Self {
        ScanConfig {
            timeout_ms: 100,
            max_concurrent: 50,
            scan_ports: false,
            common_ports: vec![22, 23, 53, 80, 135, 139, 443, 445, 993, 995, 1723, 3389, 5900, 8080],
            fast_mode: true,
        }
    }
}

#[command]
async fn scan_lan_with_progress(
    window: Window,
    config: Option<ScanConfig>
) -> Result<Vec<DeviceInfo>, String> {
    let config = config.unwrap_or_default();
    
    // Get local network info
    let local = local_ip().map_err(|e| format!("Không lấy được IP cục bộ: {}", e))?;
    let (network_prefix, local_host) = match local {
        IpAddr::V4(ipv4) => {
            let octets = ipv4.octets();
            (format!("{}.{}.{}", octets[0], octets[1], octets[2]), octets[3])
        },
        _ => return Err("Không hỗ trợ IPv6".to_string()),
    };

    // Generate IP range (exclude gateway and broadcast)
    let mut ips: Vec<u8> = (2..254).filter(|&i| i != local_host).collect();
    
    // Always scan gateway first
    ips.insert(0, 1);
    
    let total_ips = ips.len();
    let found_devices = Arc::new(Mutex::new(Vec::new()));
    let progress_counter = Arc::new(Mutex::new(0));

    // Pre-populate ARP table for better MAC lookup
    let _ = populate_arp_table(&network_prefix).await;

    // Create custom thread pool
    let pool = rayon::ThreadPoolBuilder::new()
        .num_threads(config.max_concurrent.min(100))
        .build()
        .map_err(|e| format!("Không thể tạo thread pool: {}", e))?;

    pool.install(|| {
        ips.into_par_iter().for_each(|ip_last| {
            let ip_str = format!("{}.{}", network_prefix, ip_last);
            
            // Update progress
            {
                let mut counter = progress_counter.lock().expect("Failed to lock progress counter");
                *counter += 1;
                let current_count = *counter;
                let found_count = found_devices.lock().expect("Failed to lock found_devices").len();
                
                let _ = window.emit("scan_progress", ScanProgress {
                    current: current_count,
                    total: total_ips,
                    found_devices: found_count,
                    scanning_ip: ip_str.clone(),
                });
            }

            if let Some(device) = scan_single_device(&ip_str, &config) {
                let mut devices = found_devices.lock().expect("Failed to lock found_devices for pushing");
                devices.push(device.clone());
                
                // Emit immediate device found event
                let _ = window.emit("device_found", device);
            }
        });
    });

    let mut final_devices = found_devices.lock().expect("Failed to lock for final result").clone();
    
    // Sort by IP address
    final_devices.sort_by(|a, b| {
        let ip_a: Vec<u32> = a.ip.split('.').map(|s| s.parse().unwrap_or(0)).collect();
        let ip_b: Vec<u32> = b.ip.split('.').map(|s| s.parse().unwrap_or(0)).collect();
        ip_a.cmp(&ip_b)
    });

    Ok(final_devices)
}

fn scan_single_device(ip_str: &str, config: &ScanConfig) -> Option<DeviceInfo> {
    let start_time = Instant::now();
    
    // First try TCP connect for faster detection
    if config.fast_mode {
        if !is_host_alive_tcp(ip_str, 100) {
            return None;
        }
    } else {
        // Use ping for more reliable detection
        if !is_host_alive_ping(ip_str, config.timeout_ms) {
            return None;
        }
    }
    
    let response_time = start_time.elapsed().as_millis() as u64;
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs();

    // Get additional info in parallel
    let ip_addr: IpAddr = ip_str.parse().ok()?;

    let (hostname, (mac, ports)) = rayon::join(
        || get_hostname(ip_str),
        || rayon::join(
            || lookup_mac_address(&ip_addr),
            || if config.scan_ports {
                Some(scan_common_ports(ip_str, &config.common_ports))
            } else {
                None
            }
        )
    );

    let manufacturer = mac.as_ref().and_then(|m| get_mac_manufacturer(m));
    let device_type = guess_device_type(&hostname, &mac, &ports);
    let os = guess_os_from_ports(&ports);

    Some(DeviceInfo {
        ip: ip_str.to_string(),
        hostname,
        mac,
        os,
        manufacturer,
        ports,
        response_time: Some(response_time),
        last_seen: timestamp,
        device_type,
    })
}

fn is_host_alive_tcp(ip: &str, timeout_ms: u64) -> bool {
    // Try multiple common ports for faster detection
    let ports = [80, 443, 22, 135, 445, 23];
    
    ports.par_iter().any(|&port| {
        if let Ok(addr) = format!("{}:{}", ip, port).parse::<SocketAddr>() {
            TcpStream::connect_timeout(&addr, Duration::from_millis(timeout_ms)).is_ok()
        } else {
            false
        }
    })
}

fn is_host_alive_ping(ip: &str, timeout_ms: u64) -> bool {
    let timeout_str = if cfg!(target_os = "windows") {
        timeout_ms.to_string()
    } else {
        (timeout_ms / 1000).max(1).to_string()
    };

    let output = if cfg!(target_os = "windows") {
        Command::new("ping")
            .args(["-n", "1", "-w", &timeout_str, ip])
            .output()
    } else if cfg!(target_os = "macos") {
        Command::new("ping")
            .args(["-c", "1", "-W", &timeout_str, ip])
            .output()
    } else {
        Command::new("ping")
            .args(["-c", "1", "-W", &timeout_str, ip])
            .output()
    };

    output.map(|o| o.status.success()).unwrap_or(false)
}

fn get_hostname(ip: &str) -> Option<String> {
    let ip_string = ip.to_string();
    // Timeout for DNS lookup
    std::thread::spawn(move || {
        let ip_addr: IpAddr = ip_string.parse().ok()?;
        dns_lookup::lookup_addr(&ip_addr).ok()
    })
    .join()
    .ok()
    .flatten()
}

// Cross-platform MAC address lookup
fn lookup_mac_address(ip: &IpAddr) -> Option<String> {
    let ip_str = ip.to_string();
    
    #[cfg(target_os = "windows")]
    {
        lookup_mac_windows(&ip_str)
    }
    
    #[cfg(target_os = "linux")]
    {
        lookup_mac_linux(&ip_str)
    }
    
    #[cfg(target_os = "macos")]
    {
        lookup_mac_macos(&ip_str)
    }
    
    #[cfg(not(any(target_os = "windows", target_os = "linux", target_os = "macos")))]
    {
        None
    }
}

#[cfg(target_os = "windows")]
fn lookup_mac_windows(ip_str: &str) -> Option<String> {
    use std::str;
    let output = Command::new("arp")
        .args(["-a"])
        .output()
        .ok()?;
    let stdout = str::from_utf8(&output.stdout).ok()?;
    
    for line in stdout.lines() {
        if line.contains(ip_str) {
            // Line format: ************    00-11-22-33-44-55   dynamic
            let parts: Vec<&str> = line.split_whitespace().collect();
            if parts.len() >= 2 && parts[0] == ip_str {
                return Some(parts[1].replace('-', ":").to_uppercase());
            }
        }
    }
    None
}

#[cfg(target_os = "linux")]
fn lookup_mac_linux(ip_str: &str) -> Option<String> {
    use std::str;
    
    // Try /proc/net/arp first
    if let Ok(arp_content) = std::fs::read_to_string("/proc/net/arp") {
        for line in arp_content.lines().skip(1) { // Skip header
            let parts: Vec<&str> = line.split_whitespace().collect();
            if parts.len() >= 4 && parts[0] == ip_str {
                let mac = parts[3].to_uppercase();
                if mac != "00:00:00:00:00:00" {
                    return Some(mac);
                }
            }
        }
    }
    
    // Fallback to arp command
    let output = Command::new("arp")
        .args(["-a"])
        .output()
        .ok()?;
    let stdout = str::from_utf8(&output.stdout).ok()?;
    
    for line in stdout.lines() {
        if line.contains(ip_str) {
            // Line format: hostname (************) at 00:11:22:33:44:55 [ether] on eth0
            if let Some(start) = line.find(" at ") {
                if let Some(end) = line[start + 4..].find(' ') {
                    let mac = &line[start + 4..start + 4 + end];
                    if mac.len() == 17 { // Standard MAC format
                        return Some(mac.to_uppercase());
                    }
                }
            }
        }
    }
    None
}

#[cfg(target_os = "macos")]
fn lookup_mac_macos(ip_str: &str) -> Option<String> {
    use std::str;
    let output = Command::new("arp")
        .args(["-a"])
        .output()
        .ok()?;
    let stdout = str::from_utf8(&output.stdout).ok()?;
    
    for line in stdout.lines() {
        if line.contains(ip_str) {
            // Line format: hostname (************) at 00:11:22:33:44:55 on en0 ifscope [ethernet]
            if let Some(start) = line.find(" at ") {
                if let Some(end) = line[start + 4..].find(' ') {
                    let mac = &line[start + 4..start + 4 + end];
                    if mac.len() == 17 { // Standard MAC format
                        return Some(mac.to_uppercase());
                    }
                }
            }
        }
    }
    None
}

// Pre-populate ARP table by pinging all IPs (helps with MAC lookup)
async fn populate_arp_table(network_prefix: &str) -> Result<(), Box<dyn std::error::Error>> {
    let ips: Vec<u8> = (1..=254).collect();
    
    // Send ping to all IPs quickly to populate ARP table
    let _: Vec<_> = ips.into_par_iter()
        .map(|ip_last| {
            let ip_str = format!("{}.{}", network_prefix, ip_last);
            // Quick ping with very short timeout
            if cfg!(target_os = "windows") {
                let _ = Command::new("ping")
                    .args(["-n", "1", "-w", "50", &ip_str])
                    .output();
            } else if cfg!(target_os = "macos") {
                let _ = Command::new("ping")
                    .args(["-c", "1", "-W", "50", &ip_str])
                    .output();
            } else {
                let _ = Command::new("ping")
                    .args(["-c", "1", "-W", "1", &ip_str])
                    .output();
            }
        })
        .collect();
    
    // Give ARP table time to populate
    tokio::time::sleep(Duration::from_millis(100)).await;
    Ok(())
}

fn scan_common_ports(ip: &str, ports: &[u16]) -> Vec<u16> {
    ports.par_iter()
        .filter_map(|&port| {
            if let Ok(addr) = format!("{}:{}", ip, port).parse::<SocketAddr>() {
                if TcpStream::connect_timeout(&addr, Duration::from_millis(200)).is_ok() {
                    Some(port)
                } else {
                    None
                }
            } else {
                None
            }
        })
        .collect()
}

// MAC manufacturer lookup (basic implementation)
fn get_mac_manufacturer(mac: &str) -> Option<String> {
    let oui = &mac[0..8]; // First 3 octets
    
    // Basic OUI database (can be extended)
    let mut oui_db = HashMap::new();
    oui_db.insert("00:50:56", "VMware, Inc.");
    oui_db.insert("00:0C:29", "VMware, Inc.");
    oui_db.insert("00:05:69", "VMware, Inc.");
    oui_db.insert("B8:27:EB", "Raspberry Pi Foundation");
    oui_db.insert("DC:A6:32", "Raspberry Pi Foundation");
    oui_db.insert("E4:5F:01", "Raspberry Pi Foundation");
    oui_db.insert("00:16:3E", "Xensource, Inc.");
    oui_db.insert("52:54:00", "QEMU/KVM");
    oui_db.insert("00:1B:21", "Intel Corporate");
    oui_db.insert("00:15:5D", "Microsoft Corporation");
    oui_db.insert("00:03:FF", "Microsoft Corporation");
    oui_db.insert("3C:07:54", "Apple, Inc.");
    oui_db.insert("A4:83:E7", "Apple, Inc.");
    oui_db.insert("28:CF:E9", "Apple, Inc.");
    
    oui_db.get(oui).map(|&s| s.to_string())
}

fn guess_device_type(hostname: &Option<String>, mac: &Option<String>, ports: &Option<Vec<u16>>) -> Option<String> {
    // Check hostname patterns
    if let Some(name) = hostname {
        let name_lower = name.to_lowercase();
        if name_lower.contains("router") || name_lower.contains("gateway") || name_lower.contains("openwrt") {
            return Some("Router".to_string());
        }
        if name_lower.contains("printer") || name_lower.contains("hp-") || name_lower.contains("canon") || name_lower.contains("epson") {
            return Some("Printer".to_string());
        }
        if name_lower.contains("android") || name_lower.contains("iphone") || name_lower.contains("ipad") {
            return Some("Mobile Device".to_string());
        }
        if name_lower.contains("raspberrypi") || name_lower.contains("pi") {
            return Some("Raspberry Pi".to_string());
        }
        if name_lower.contains("nas") || name_lower.contains("synology") || name_lower.contains("qnap") {
            return Some("NAS Device".to_string());
        }
        if name_lower.contains("smart") || name_lower.contains("iot") {
            return Some("IoT Device".to_string());
        }
    }

    // Check MAC manufacturer
    if let Some(mac_addr) = mac {
        if mac_addr.starts_with("B8:27:EB") || mac_addr.starts_with("DC:A6:32") || mac_addr.starts_with("E4:5F:01") {
            return Some("Raspberry Pi".to_string());
        }
        if mac_addr.starts_with("00:50:56") || mac_addr.starts_with("00:0C:29") || mac_addr.starts_with("52:54:00") {
            return Some("Virtual Machine".to_string());
        }
        if mac_addr.starts_with("3C:07:54") || mac_addr.starts_with("A4:83:E7") || mac_addr.starts_with("28:CF:E9") {
            return Some("Apple Device".to_string());
        }
    }

    // Check open ports
    if let Some(open_ports) = ports {
        if open_ports.contains(&3389) {
            return Some("Windows PC".to_string());
        }
        if open_ports.contains(&22) && !open_ports.contains(&3389) {
            return Some("Linux/Unix Server".to_string());
        }
        if open_ports.contains(&631) || open_ports.contains(&9100) {
            return Some("Printer".to_string());
        }
        if open_ports.contains(&80) && open_ports.contains(&443) && !open_ports.contains(&22) {
            return Some("Web Server/Router".to_string());
        }
        if open_ports.contains(&5000) || open_ports.contains(&5001) {
            return Some("NAS/Media Server".to_string());
        }
    }

    Some("Unknown Device".to_string())
}

fn guess_os_from_ports(ports: &Option<Vec<u16>>) -> Option<String> {
    if let Some(open_ports) = ports {
        if open_ports.contains(&3389) {
            return Some("Windows".to_string());
        }
        if open_ports.contains(&22) && !open_ports.contains(&3389) {
            if open_ports.contains(&80) || open_ports.contains(&443) {
                return Some("Linux Server".to_string());
            }
            return Some("Linux/Unix".to_string());
        }
        if open_ports.contains(&445) && open_ports.contains(&135) {
            return Some("Windows".to_string());
        }
        if open_ports.contains(&548) {
            return Some("macOS".to_string());
        }
    }
    None
}

#[command]
async fn quick_scan() -> Result<Vec<String>, String> {
    let local = local_ip().map_err(|e| format!("Không lấy được IP cục bộ: {}", e))?;
    let network_prefix = match local {
        IpAddr::V4(ipv4) => {
            let octets = ipv4.octets();
            format!("{}.{}.{}", octets[0], octets[1], octets[2])
        },
        _ => return Err("Không hỗ trợ IPv6".to_string()),
    };

    let ips: Vec<u8> = (1..=254).collect();
    
    let alive_ips: Vec<String> = ips.into_par_iter()
        .filter_map(|ip_last| {
            let ip_str = format!("{}.{}", network_prefix, ip_last);
            if is_host_alive_tcp(&ip_str, 50) {
                Some(ip_str)
            } else {
                None
            }
        })
        .collect();

    Ok(alive_ips)
}

#[command]
fn get_local_ip() -> Result<String, String> {
    local_ip()
        .map(|ip| ip.to_string())
        .map_err(|e| format!("Không lấy được IP cục bộ: {}", e))
}

#[command]
fn get_network_info() -> Result<serde_json::Value, String> {
    let local = local_ip().map_err(|e| format!("Không lấy được IP cục bộ: {}", e))?;
    
    match local {
        IpAddr::V4(ipv4) => {
            let octets = ipv4.octets();
            Ok(serde_json::json!({
                "local_ip": ipv4.to_string(),
                "network": format!("{}.{}.{}.0/24", octets[0], octets[1], octets[2]),
                "gateway": format!("{}.{}.{}.1", octets[0], octets[1], octets[2]),
                "broadcast": format!("{}.{}.{}.255", octets[0], octets[1], octets[2]),
                "os": std::env::consts::OS
            }))
        },
        _ => Err("Không hỗ trợ IPv6".to_string()),
    }
}

#[command]
async fn get_system_info() -> Result<serde_json::Value, String> {
    Ok(serde_json::json!({
        "os": std::env::consts::OS,
        "arch": std::env::consts::ARCH,
        "family": std::env::consts::FAMILY,
        "version": option_env!("CARGO_PKG_VERSION").unwrap_or("unknown")
    }))
}

// Thêm struct cho cấu hình firewall
#[derive(Debug, Serialize, Deserialize)]
struct FirewallRule {
    name: String,
    port: u16,
    protocol: String, // TCP/UDP
    action: String,   // ALLOW/BLOCK
    direction: String, // IN/OUT
}

#[derive(Debug, Serialize, Deserialize)]
struct FirewallApp {
    name: String,
    path: String,
    action: String, // ALLOW/BLOCK
}

// Chức năng quản lý firewall ports
#[command]
async fn manage_firewall_port(rule: FirewallRule, action: String) -> Result<String, String> {
    #[cfg(target_os = "windows")]
    {
        manage_windows_firewall_port(rule, action).await
    }
    
    #[cfg(target_os = "linux")]
    {
        manage_linux_firewall_port(rule, action).await
    }
    
    #[cfg(target_os = "macos")]
    {
        manage_macos_firewall_port(rule, action).await
    }
    
    #[cfg(not(any(target_os = "windows", target_os = "linux", target_os = "macos")))]
    {
        Err("Hệ điều hành không được hỗ trợ".to_string())
    }
}

#[cfg(target_os = "windows")]
async fn manage_windows_firewall_port(rule: FirewallRule, action: String) -> Result<String, String> {
    let rule_name = format!("LAN_Scanner_{}", rule.name);
    
    if action == "ADD" {
        let output = Command::new("netsh")
            .args([
                "advfirewall", "firewall", "add", "rule",
                &format!("name={}", rule_name),
                &format!("dir={}", rule.direction.to_lowercase()),
                &format!("action={}", rule.action.to_lowercase()),
                &format!("protocol={}", rule.protocol.to_lowercase()),
                &format!("localport={}", rule.port)
            ])
            .output()
            .map_err(|e| format!("Lỗi thực thi lệnh: {}", e))?;
            
        if output.status.success() {
            Ok(format!("Đã thêm rule: {}", rule_name))
        } else {
            Err(format!("Lỗi: {}", String::from_utf8_lossy(&output.stderr)))
        }
    } else if action == "REMOVE" {
        let output = Command::new("netsh")
            .args([
                "advfirewall", "firewall", "delete", "rule",
                &format!("name={}", rule_name)
            ])
            .output()
            .map_err(|e| format!("Lỗi thực thi lệnh: {}", e))?;
            
        if output.status.success() {
            Ok(format!("Đã xóa rule: {}", rule_name))
        } else {
            Err(format!("Lỗi: {}", String::from_utf8_lossy(&output.stderr)))
        }
    } else {
        Err("Action không hợp lệ".to_string())
    }
}

// Chức năng quản lý firewall applications
#[command]
async fn manage_firewall_app(app: FirewallApp, action: String) -> Result<String, String> {
    #[cfg(target_os = "windows")]
    {
        manage_windows_firewall_app(app, action).await
    }
    
    #[cfg(not(target_os = "windows"))]
    {
        Err("Chức năng này chỉ hỗ trợ Windows".to_string())
    }
}

#[cfg(target_os = "windows")]
async fn manage_windows_firewall_app(app: FirewallApp, action: String) -> Result<String, String> {
    let rule_name = format!("LAN_Scanner_App_{}", app.name);
    
    if action == "ADD" {
        let output = Command::new("netsh")
            .args([
                "advfirewall", "firewall", "add", "rule",
                &format!("name={}", rule_name),
                "dir=in",
                &format!("action={}", app.action.to_lowercase()),
                &format!("program={}", app.path)
            ])
            .output()
            .map_err(|e| format!("Lỗi thực thi lệnh: {}", e))?;
            
        if output.status.success() {
            Ok(format!("Đã thêm app rule: {}", rule_name))
        } else {
            Err(format!("Lỗi: {}", String::from_utf8_lossy(&output.stderr)))
        }
    } else if action == "REMOVE" {
        let output = Command::new("netsh")
            .args([
                "advfirewall", "firewall", "delete", "rule",
                &format!("name={}", rule_name)
            ])
            .output()
            .map_err(|e| format!("Lỗi thực thi lệnh: {}", e))?;
            
        if output.status.success() {
            Ok(format!("Đã xóa app rule: {}", rule_name))
        } else {
            Err(format!("Lỗi: {}", String::from_utf8_lossy(&output.stderr)))
        }
    } else {
        Err("Action không hợp lệ".to_string())
    }
}

// Lấy danh sách firewall rules
#[command]
async fn get_firewall_rules() -> Result<Vec<String>, String> {
    #[cfg(target_os = "windows")]
    {
        let output = Command::new("netsh")
            .args(["advfirewall", "firewall", "show", "rule", "name=all"])
            .output()
            .map_err(|e| format!("Lỗi thực thi lệnh: {}", e))?;
            
        if output.status.success() {
            let rules: Vec<String> = String::from_utf8_lossy(&output.stdout)
                .lines()
                .filter(|line| line.starts_with("Rule Name:"))
                .map(|line| line.replace("Rule Name:", "").trim().to_string())
                .collect();
            Ok(rules)
        } else {
            Err("Không thể lấy danh sách rules".to_string())
        }
    }
    
    #[cfg(not(target_os = "windows"))]
    {
        Err("Chức năng này chỉ hỗ trợ Windows".to_string())
    }
}

fn main() {
    tauri::Builder::default()
        .invoke_handler(tauri::generate_handler![
            scan_lan_with_progress,
            quick_scan,
            get_local_ip,
            get_network_info,
            get_system_info,
            manage_firewall_port,
            manage_firewall_app,
            get_firewall_rules
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

