# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-is-visible"
description = "Enables the is_visible command without any pre-configured scope."
commands.allow = ["is_visible"]

[[permission]]
identifier = "deny-is-visible"
description = "Denies the is_visible command without any pre-configured scope."
commands.deny = ["is_visible"]
