{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 1047385865093030121, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 9915656377992783023], [3150220818285335163, "url", false, 9149914415962147110], [3191507132440681679, "serde_untagged", false, 7924143659851940891], [4071963112282141418, "serde_with", false, 15161175065129917141], [4899080583175475170, "semver", false, 12804167403314561573], [5986029879202738730, "log", false, 15765517366915710996], [6606131838865521726, "ctor", false, 13472640333893764312], [7170110829644101142, "json_patch", false, 1304960711701311724], [8319709847752024821, "uuid", false, 4010746127231657758], [8569119365930580996, "serde_json", false, 2806873895409105749], [9010263965687315507, "http", false, 16986275522130494756], [9451456094439810778, "regex", false, 17930507548507241716], [9556762810601084293, "brotli", false, 7772046722291759537], [9689903380558560274, "serde", false, 8511114661884439858], [10806645703491011684, "thiserror", false, 10064581045881432056], [11989259058781683633, "dunce", false, 17317098620664756496], [13625485746686963219, "anyhow", false, 1552347067440083334], [15609422047640926750, "toml", false, 9900471989968330014], [15622660310229662834, "walkdir", false, 16689586081458605815], [15932120279885307830, "memchr", false, 10572322225675336786], [17146114186171651583, "infer", false, 5306484083609629546], [17155886227862585100, "glob", false, 17854795776696857982], [17186037756130803222, "phf", false, 18383277827893919756]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-2548f89b3d10329b\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}