{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 9311417598471442982, "deps": [[654232091421095663, "tauri_utils", false, 10591440845926958216], [3150220818285335163, "url", false, 9149914415962147110], [4143744114649553716, "raw_window_handle", false, 4799670219169884245], [7606335748176206944, "dpi", false, 7081146018274830431], [8569119365930580996, "serde_json", false, 2806873895409105749], [9010263965687315507, "http", false, 16986275522130494756], [9689903380558560274, "serde", false, 8511114661884439858], [10806645703491011684, "thiserror", false, 10064581045881432056], [12943761728066819757, "build_script_build", false, 1115477786926781373], [14585479307175734061, "windows", false, 17088300214248134231], [16727543399706004146, "cookie", false, 16752923613538458469]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-fa8719322e0d250d\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}