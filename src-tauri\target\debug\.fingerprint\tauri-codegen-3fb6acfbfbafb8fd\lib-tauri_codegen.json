{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 44390266975535834, "deps": [[654232091421095663, "tauri_utils", false, 3534115001892356441], [3060637413840920116, "proc_macro2", false, 2079900143197701277], [3150220818285335163, "url", false, 9408122095897454170], [4899080583175475170, "semver", false, 4831674333348931428], [4974441333307933176, "syn", false, 3050935563027769116], [7170110829644101142, "json_patch", false, 2923502991744180563], [7392050791754369441, "ico", false, 4928249365220759324], [8319709847752024821, "uuid", false, 16726313797076576013], [8569119365930580996, "serde_json", false, 13498032808021772243], [9556762810601084293, "brotli", false, 3509722801535583509], [9689903380558560274, "serde", false, 9246617389178666882], [9857275760291862238, "sha2", false, 16703671587864551489], [10806645703491011684, "thiserror", false, 1195389295674622069], [12687914511023397207, "png", false, 8015420232515873259], [13077212702700853852, "base64", false, 17182321280524970086], [15622660310229662834, "walkdir", false, 3021204979405554598], [17990358020177143287, "quote", false, 4602638336399811457]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-3fb6acfbfbafb8fd\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}