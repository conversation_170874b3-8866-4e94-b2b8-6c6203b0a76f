{"rustc": 1842507548689473721, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 14646911496163987231, "deps": [[654232091421095663, "tauri_utils", false, 8085405789211869638], [4824857623768494398, "cargo_toml", false, 4540034889526286625], [4899080583175475170, "semver", false, 4831674333348931428], [6913375703034175521, "schemars", false, 12921579865055614397], [7170110829644101142, "json_patch", false, 5047771166925430042], [8569119365930580996, "serde_json", false, 16182023219926506035], [9689903380558560274, "serde", false, 9246617389178666882], [12714016054753183456, "tauri_winres", false, 12652149000889543454], [13077543566650298139, "heck", false, 3133400880998861990], [13625485746686963219, "anyhow", false, 1552347067440083334], [15609422047640926750, "toml", false, 3507140950580140327], [15622660310229662834, "walkdir", false, 1349346429262320482], [16928111194414003569, "dirs", false, 1615057664575289234], [17155886227862585100, "glob", false, 17854795776696857982]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-c4b0d1674494ff9b\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}