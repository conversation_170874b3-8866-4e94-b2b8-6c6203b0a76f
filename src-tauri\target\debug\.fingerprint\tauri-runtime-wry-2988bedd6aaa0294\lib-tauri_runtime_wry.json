{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 2241668132362809309, "path": 4850902529613245248, "deps": [[376837177317575824, "softbuffer", false, 3157289671960304048], [654232091421095663, "tauri_utils", false, 7695265353245193155], [2013030631243296465, "webview2_com", false, 9214410664057349386], [3150220818285335163, "url", false, 476110366268104882], [3722963349756955755, "once_cell", false, 17696487311917879680], [4143744114649553716, "raw_window_handle", false, 16341307260128210312], [5986029879202738730, "log", false, 14539903849470444598], [8826339825490770380, "tao", false, 7363759923870421473], [9010263965687315507, "http", false, 1782551203943155301], [9141053277961803901, "wry", false, 14441883226436950906], [12304025191202589669, "build_script_build", false, 12674754355741948356], [12943761728066819757, "tauri_runtime", false, 17857731071459737596], [14585479307175734061, "windows", false, 7082548166437213722]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-2988bedd6aaa0294\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}