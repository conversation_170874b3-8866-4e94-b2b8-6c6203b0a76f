["\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\app\\autogenerated\\commands\\app_hide.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\app\\autogenerated\\commands\\app_show.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\app\\autogenerated\\commands\\bundle_type.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\app\\autogenerated\\commands\\default_window_icon.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\app\\autogenerated\\commands\\fetch_data_store_identifiers.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\app\\autogenerated\\commands\\identifier.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\app\\autogenerated\\commands\\name.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\app\\autogenerated\\commands\\remove_data_store.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\app\\autogenerated\\commands\\set_app_theme.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\app\\autogenerated\\commands\\set_dock_visibility.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\app\\autogenerated\\commands\\tauri_version.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\app\\autogenerated\\commands\\version.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\app\\autogenerated\\default.toml"]