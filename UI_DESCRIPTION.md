# Mô Tả Giao Diện Quản Lý Firewall

## Tổng Quan Giao Diện

Ứng dụng có 3 tab chính với thiết kế hiện đại và dễ sử dụng:

```
┌─────────────────────────────────────────────────────────────┐
│  LAN Scanner & Firewall Manager                             │
├─────────────────────────────────────────────────────────────┤
│  [Quét Mạng LAN] [Quản Lý Port] [Quản Lý Ứng Dụng]        │
└─────────────────────────────────────────────────────────────┘
```

## Tab 1: Quét Mạng LAN (Hiện có)
- <PERSON><PERSON><PERSON> "Bắt Đầu Quét" màu xanh lá
- Bảng hiển thị thiết bị với các cột: IP, Hostname, MAC, OS, Device Type, Manufacturer
- Progress bar khi đang quét

## Tab 2: Quản Lý Port Tường Lửa

### Phần 1: Trạng Thái Firewall
```
┌─────────────────────────────────────────────────────────────┐
│  🛡️ Trạng Thái Tường Lửa                                    │
├─────────────────────────────────────────────────────────────┤
│  [🛡️ Enabled]    [🛡️ Enabled]    [❌ Disabled]            │
│  Domain Profile   Private Profile  Public Profile           │
│                                                             │
│  Trạng thái tổng thể: Enabled                              │
│  [Làm Mới Trạng Thái]                                      │
└─────────────────────────────────────────────────────────────┘
```

### Phần 2: Form Thêm Rule
```
┌─────────────────────────────────────────────────────────────┐
│  Tên Rule: [Web Server Rule________________]                │
│                                                             │
│  Cấu hình Port:                                            │
│  ○ Port đơn  ○ Dải port  ○ Tất cả port                    │
│                                                             │
│  Port: [80_________________________]                       │
│  Ví dụ: 80, 443, 8000-8080                                │
│                                                             │
│  Protocol: [TCP ▼]                                         │
│  Hành động: [Cho phép (Allow) ▼]                          │
│  Hướng: [Inbound (Vào) ▼]                                 │
│  Phạm vi: [Tất cả địa chỉ ▼]                              │
│                                                             │
│  [Thêm Rule] [Xóa Rule Đã Chọn] [Làm Mới] [Xuất Cấu Hình] │
└─────────────────────────────────────────────────────────────┘
```

### Phần 3: Bảng Rules Hiện Có
```
┌─────────────────────────────────────────────────────────────┐
│  Danh Sách Rules Hiện Có                                   │
├─────────────────────────────────────────────────────────────┤
│  ☑ │ Tên Rule    │ Port │ Protocol │ Hành động │ Trạng thái │
│  ☐ │ Web Server  │ 80   │ TCP      │ Allow     │ Enabled   │
│  ☐ │ SSH Access  │ 22   │ TCP      │ Allow     │ Enabled   │
│  ☐ │ Game Port   │8000-8│ UDP      │ Block     │ Disabled  │
└─────────────────────────────────────────────────────────────┘
```

## Tab 3: Quản Lý Ứng Dụng

### Phần 1: Form Thêm App Rule
```
┌─────────────────────────────────────────────────────────────┐
│  Tên Ứng Dụng: [Chrome Browser_______________]              │
│                                                             │
│  Cách chọn ứng dụng:                                       │
│  ○ Nhập đường dẫn  ○ Duyệt file  ○ Chọn từ app đang chạy  │
│                                                             │
│  Đường Dẫn: [C:\Program Files\Google\Chrome\chrome.exe___] │
│  Đường dẫn đầy đủ đến file .exe của ứng dụng              │
│                                                             │
│  Hành động: [Cho phép (Allow) ▼]                          │
│  Phạm vi: [Tất cả kết nối ▼]                              │
│                                                             │
│  Ghi chú: [Trình duyệt web chính_______________]           │
│           [_________________________________]               │
│                                                             │
│  [Thêm Rule] [Xóa Rule] [Làm Mới] [Xuất Cấu Hình]        │
└─────────────────────────────────────────────────────────────┘
```

### Phần 2: Chọn App Đang Chạy (khi chọn radio button)
```
┌─────────────────────────────────────────────────────────────┐
│  Ứng Dụng Đang Chạy: [chrome.exe (C:\Program Files\...)▼] │
│  [Làm Mới]                                                 │
└─────────────────────────────────────────────────────────────┘
```

### Phần 3: Bảng App Rules
```
┌─────────────────────────────────────────────────────────────┐
│  Danh Sách Rules Ứng Dụng Hiện Có                         │
├─────────────────────────────────────────────────────────────┤
│  ☑ │ Tên App │ Đường Dẫn │ Hành động │ Phạm vi │ Trạng thái │
│  ☐ │ Chrome  │ C:\Prog.. │ Allow     │ All     │ Enabled   │
│  ☐ │ Steam   │ C:\Prog.. │ Block     │ Internet│ Disabled  │
└─────────────────────────────────────────────────────────────┘
```

## Màu Sắc và Thiết Kế

### Màu Chủ Đạo:
- **Xanh lá (#4CAF50)**: Nút thêm, trạng thái enabled
- **Đỏ (#f44336)**: Nút xóa, trạng thái disabled  
- **Xanh dương (#2196F3)**: Nút làm mới, tab active
- **Cam (#FF9800)**: Nút chỉnh sửa, trạng thái unknown
- **Tím (#9C27B0)**: Nút xuất cấu hình

### Hiệu Ứng:
- Hover effect: Nút nhấc lên và đổi độ mờ
- Transition mượt mà khi chuyển tab
- Border highlight khi focus vào input
- Zebra striping cho bảng (dòng chẵn/lẻ khác màu)

### Responsive:
- Form tự động điều chỉnh theo kích thước cửa sổ
- Bảng có scroll ngang khi cần
- Grid layout cho trạng thái firewall

## Tương Tác Người Dùng

### Validation:
- Kiểm tra tên rule không được trống
- Validate format port (1-65535)
- Validate dải port (format: start-end)
- Kiểm tra đường dẫn ứng dụng tồn tại

### Feedback:
- Thông báo thành công màu xanh
- Thông báo lỗi màu đỏ
- Loading spinner khi đang xử lý
- Confirm dialog khi xóa

### Keyboard Support:
- Tab navigation giữa các field
- Enter để submit form
- Space để toggle checkbox
- Escape để cancel dialog

## Tính Năng Đặc Biệt

### Batch Operations:
- Checkbox "Chọn tất cả" ở header bảng
- Xóa nhiều rules cùng lúc
- Bulk enable/disable (sẽ phát triển)

### Smart Defaults:
- Protocol mặc định: TCP
- Hành động mặc định: Allow
- Hướng mặc định: Inbound
- Phạm vi mặc định: Tất cả địa chỉ

### Auto-refresh:
- Tự động làm mới danh sách sau khi thêm/xóa
- Cập nhật trạng thái firewall định kỳ
- Sync với Windows Firewall settings

Giao diện được thiết kế theo nguyên tắc Material Design với focus vào usability và accessibility, đảm bảo người dùng có thể dễ dàng quản lý firewall mà không cần kiến thức chuyên sâu.
