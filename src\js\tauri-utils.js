// Tauri utilities for safe API access
export class TauriUtils {
  static async waitForTauri(timeout = 5000) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      const checkTauri = () => {
        if (window.__TAURI__ && window.__TAURI__.tauri && window.__TAURI__.tauri.invoke) {
          resolve(window.__TAURI__.tauri.invoke);
        } else if (Date.now() - startTime > timeout) {
          reject(new Error('Tauri API not available after timeout'));
        } else {
          setTimeout(checkTauri, 50);
        }
      };
      
      checkTauri();
    });
  }

  static async safeInvoke(command, args = {}) {
    try {
      const invoke = await this.waitForTauri();
      return await invoke(command, args);
    } catch (error) {
      console.error(`Error invoking ${command}:`, error);
      throw error;
    }
  }

  static isTauriReady() {
    return !!(window.__TAURI__ && window.__TAURI__.tauri && window.__TAURI__.tauri.invoke);
  }
}

// Export a singleton instance
export const tauriUtils = new TauriUtils();
