{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 18393454837022506084, "deps": [[40386456601120721, "percent_encoding", false, 1702795566160350733], [654232091421095663, "tauri_utils", false, 10591440845926958216], [1200537532907108615, "url<PERSON><PERSON>n", false, 9915656377992783023], [2013030631243296465, "webview2_com", false, 16687020024527186247], [3150220818285335163, "url", false, 9149914415962147110], [3331586631144870129, "getrandom", false, 5708651109123588408], [4143744114649553716, "raw_window_handle", false, 4799670219169884245], [4494683389616423722, "muda", false, 10610156855792689157], [4919829919303820331, "serialize_to_javascript", false, 3635216395798035189], [5986029879202738730, "log", false, 15765517366915710996], [8569119365930580996, "serde_json", false, 2806873895409105749], [9010263965687315507, "http", false, 16986275522130494756], [9689903380558560274, "serde", false, 8511114661884439858], [10229185211513642314, "mime", false, 12664307337545127514], [10806645703491011684, "thiserror", false, 10064581045881432056], [11989259058781683633, "dunce", false, 17317098620664756496], [12092653563678505622, "build_script_build", false, 12216727744425479957], [12304025191202589669, "tauri_runtime_wry", false, 1522540996912029731], [12565293087094287914, "window_vibrancy", false, 5673541920948309090], [12943761728066819757, "tauri_runtime", false, 10800498006564925613], [12944427623413450645, "tokio", false, 13902341140467724027], [12986574360607194341, "serde_repr", false, 15342612029808804463], [13077543566650298139, "heck", false, 3133400880998861990], [13405681745520956630, "tauri_macros", false, 5849442168102590786], [13625485746686963219, "anyhow", false, 1552347067440083334], [14585479307175734061, "windows", false, 17088300214248134231], [16928111194414003569, "dirs", false, 1615057664575289234], [17155886227862585100, "glob", false, 17854795776696857982]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-0d300d2bfa4706fc\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}