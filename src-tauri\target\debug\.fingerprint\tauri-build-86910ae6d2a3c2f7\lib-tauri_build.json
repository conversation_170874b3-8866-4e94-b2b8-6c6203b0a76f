{"rustc": 1842507548689473721, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 14646911496163987231, "deps": [[654232091421095663, "tauri_utils", false, 3534115001892356441], [4824857623768494398, "cargo_toml", false, 6172311291754949196], [4899080583175475170, "semver", false, 4831674333348931428], [6913375703034175521, "schemars", false, 8453878774288219916], [7170110829644101142, "json_patch", false, 2923502991744180563], [8569119365930580996, "serde_json", false, 13498032808021772243], [9689903380558560274, "serde", false, 9246617389178666882], [12714016054753183456, "tauri_winres", false, 13196005976781615425], [13077543566650298139, "heck", false, 12635769686148833666], [13625485746686963219, "anyhow", false, 3358966957246449338], [15609422047640926750, "toml", false, 712743537792800879], [15622660310229662834, "walkdir", false, 3021204979405554598], [16928111194414003569, "dirs", false, 14024474966584132035], [17155886227862585100, "glob", false, 1752858758367815227]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-86910ae6d2a3c2f7\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}