["\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\menu\\autogenerated\\commands\\append.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\menu\\autogenerated\\commands\\create_default.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\menu\\autogenerated\\commands\\get.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\menu\\autogenerated\\commands\\insert.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\menu\\autogenerated\\commands\\is_checked.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\menu\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\menu\\autogenerated\\commands\\items.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\menu\\autogenerated\\commands\\new.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\menu\\autogenerated\\commands\\popup.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\menu\\autogenerated\\commands\\prepend.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\menu\\autogenerated\\commands\\remove.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\menu\\autogenerated\\commands\\remove_at.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\menu\\autogenerated\\commands\\set_accelerator.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\menu\\autogenerated\\commands\\set_as_app_menu.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\menu\\autogenerated\\commands\\set_as_help_menu_for_nsapp.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\menu\\autogenerated\\commands\\set_as_window_menu.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\menu\\autogenerated\\commands\\set_as_windows_menu_for_nsapp.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\menu\\autogenerated\\commands\\set_checked.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\menu\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\menu\\autogenerated\\commands\\set_icon.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\menu\\autogenerated\\commands\\set_text.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\menu\\autogenerated\\commands\\text.toml", "\\\\?\\E:\\Project\\tauri\\network-scanner\\src-tauri\\target\\debug\\build\\tauri-583c14e16aa7e9dc\\out\\permissions\\menu\\autogenerated\\default.toml"]